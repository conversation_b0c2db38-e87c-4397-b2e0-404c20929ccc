/**
 * requestHeaderService.js
 * 
 * 使用Chrome扩展的webRequest API来修改下载请求的请求头
 * 解决fetch API无法设置某些请求头（如Referer）的问题
 */

import { logger } from '../../util/logger.js';

class RequestHeaderService {
  constructor() {
    this.activeDownloads = new Map(); // 存储活跃的下载请求
    this.isListenerRegistered = false;
  }

  /**
   * 为特定URL注册请求头修改
   * @param {string} url 要修改请求头的URL
   * @param {Object} headers 要设置的请求头
   * @param {string} downloadId 下载ID（用于跟踪）
   */
  registerDownloadHeaders(url, headers, downloadId) {
    logger.debug(`[RequestHeaderService] 注册下载请求头修改: ${url}`);
    
    this.activeDownloads.set(url, {
      headers: headers,
      downloadId: downloadId,
      timestamp: Date.now()
    });
    
    // 确保监听器已注册
    this.ensureListenerRegistered();
    
    // 5分钟后自动清理
    setTimeout(() => {
      this.activeDownloads.delete(url);
      logger.debug(`[RequestHeaderService] 清理过期的下载请求: ${url}`);
    }, 5 * 60 * 1000);
  }

  /**
   * 确保webRequest监听器已注册
   */
  ensureListenerRegistered() {
    if (this.isListenerRegistered) {
      return;
    }

    try {
      // 使用非阻塞方式监听请求（仅用于日志记录）
      chrome.webRequest.onBeforeSendHeaders.addListener(
        (details) => this.logRequestHeaders(details),
        { urls: ["<all_urls>"] },
        ["requestHeaders"]
      );

      this.isListenerRegistered = true;
      logger.debug(`[RequestHeaderService] webRequest监听器已注册（非阻塞模式）`);
    } catch (error) {
      logger.error(`[RequestHeaderService] 注册webRequest监听器失败:`, error);
    }
  }

  /**
   * 记录请求头（非阻塞模式）
   * @param {Object} details webRequest详情
   */
  logRequestHeaders(details) {
    const url = details.url;

    // 检查是否是我们关注的下载请求
    const downloadInfo = this.activeDownloads.get(url);
    if (!downloadInfo) {
      return; // 不记录
    }

    logger.debug(`[RequestHeaderService] 检测到目标请求: ${url}`);
    logger.debug(`[RequestHeaderService] 实际发送的请求头:`, details.requestHeaders);

    // 检查关键请求头是否存在
    const headers = details.requestHeaders;
    const referer = headers.find(h => h.name.toLowerCase() === 'referer');
    const userAgent = headers.find(h => h.name.toLowerCase() === 'user-agent');

    logger.debug(`[RequestHeaderService] Referer: ${referer ? referer.value : '未设置'}`);
    logger.debug(`[RequestHeaderService] User-Agent: ${userAgent ? userAgent.value : '未设置'}`);

    // 清理已处理的下载请求
    this.activeDownloads.delete(url);
  }

  /**
   * 为Academia.edu下载注册特殊请求头
   * @param {string} url Academia.edu的PDF URL
   * @param {string} downloadId 下载ID
   */
  registerAcademiaHeaders(url, downloadId) {
    const headers = {
      'Referer': 'https://scholar.google.com/',
      'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36 Edg/139.0.0.0',
      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
      'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6,zh-TW;q=0.5',
      'Sec-Fetch-Dest': 'document',
      'Sec-Fetch-Mode': 'navigate',
      'Sec-Fetch-Site': 'cross-site',
      'Sec-Fetch-User': '?1',
      'Upgrade-Insecure-Requests': '1'
    };
    
    logger.debug(`[RequestHeaderService] 为Academia.edu注册特殊请求头`);
    this.registerDownloadHeaders(url, headers, downloadId);
  }

  /**
   * 清理所有活跃的下载请求
   */
  clearAllDownloads() {
    logger.debug(`[RequestHeaderService] 清理所有活跃下载请求`);
    this.activeDownloads.clear();
  }

  /**
   * 获取活跃下载数量
   */
  getActiveDownloadsCount() {
    return this.activeDownloads.size;
  }

  /**
   * 测试webRequest API是否能设置Referer请求头
   */
  async testRefererHeader() {
    logger.debug(`[RequestHeaderService] 开始测试Referer请求头设置`);

    // 测试URL - 使用httpbin.org来检查请求头
    const testUrl = 'https://httpbin.org/headers';

    // 注册测试请求头
    const testHeaders = {
      'Referer': 'https://scholar.google.com/',
      'X-Test-Header': 'WebRequest-Test'
    };

    this.registerDownloadHeaders(testUrl, testHeaders, 'test-referer');

    try {
      // 发送测试请求
      const response = await fetch(testUrl);
      const result = await response.json();

      logger.debug(`[RequestHeaderService] 服务器收到的请求头:`, result.headers);

      // 检查Referer是否成功设置
      const refererSet = result.headers.Referer || result.headers.referer;
      const testHeaderSet = result.headers['X-Test-Header'] || result.headers['x-test-header'];

      const testResult = {
        success: !!(refererSet && testHeaderSet),
        refererSet: !!refererSet,
        refererValue: refererSet,
        testHeaderSet: !!testHeaderSet,
        allHeaders: result.headers
      };

      logger.debug(`[RequestHeaderService] 测试结果:`, testResult);

      if (testResult.success) {
        logger.debug(`[RequestHeaderService] ✅ webRequest API测试成功！`);
      } else {
        logger.warn(`[RequestHeaderService] ❌ webRequest API测试失败`);
      }

      return testResult;

    } catch (error) {
      logger.error(`[RequestHeaderService] 测试失败:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 测试Academia.edu的重定向
   */
  async testAcademiaRedirect() {
    logger.debug(`[RequestHeaderService] 开始测试Academia.edu重定向`);

    const testUrl = 'https://www.academia.edu/download/35254130/PR-1963_70_80-90.pdf';

    // 注册Academia.edu请求头
    this.registerAcademiaHeaders(testUrl, 'test-academia');

    try {
      // 使用fetch测试，应该被webRequest拦截
      const response = await fetch(testUrl, {
        method: 'HEAD',
        redirect: 'manual'
      });

      const result = {
        status: response.status,
        location: response.headers.get('location'),
        contentType: response.headers.get('content-type'),
        redirected: response.status >= 300 && response.status < 400
      };

      logger.debug(`[RequestHeaderService] Academia.edu测试结果:`, result);

      // 检查是否获得了重定向
      if (result.redirected && result.location) {
        if (result.location.includes('cloudfront.net') || result.location.includes('.pdf')) {
          logger.debug(`[RequestHeaderService] ✅ 成功获取PDF重定向URL!`);
          result.success = true;
          result.pdfUrl = result.location;
        } else {
          logger.warn(`[RequestHeaderService] ❌ 重定向到非PDF URL: ${result.location}`);
          result.success = false;
        }
      } else {
        logger.warn(`[RequestHeaderService] ❌ 没有获得重定向响应`);
        result.success = false;
      }

      return result;

    } catch (error) {
      logger.error(`[RequestHeaderService] Academia.edu测试失败:`, error);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

// 创建单例实例
const requestHeaderService = new RequestHeaderService();

// 在全局作用域中添加测试函数（用于调试）
if (typeof globalThis !== 'undefined') {
  globalThis.testRefererHeader = () => requestHeaderService.testRefererHeader();
  globalThis.testAcademiaRedirect = () => requestHeaderService.testAcademiaRedirect();
  globalThis.getActiveDownloads = () => requestHeaderService.getActiveDownloadsCount();
}

export { requestHeaderService };
export default requestHeaderService;
