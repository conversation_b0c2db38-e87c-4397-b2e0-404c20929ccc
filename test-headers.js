/**
 * 测试不同请求头组合对Academia.edu重定向的影响
 */

async function testHeaderCombinations() {
  const testUrl = 'https://www.academia.edu/download/35254130/PR-1963_70_80-90.pdf';
  
  console.log('=== 测试不同请求头组合 ===');
  console.log('目标URL:', testUrl);
  
  // 测试1: 基础请求（无额外请求头）
  console.log('\n1. 基础请求（无额外请求头）');
  await testWithHeaders(testUrl, {}, '基础请求');
  
  // 测试2: 只添加User-Agent
  console.log('\n2. 只添加User-Agent');
  await testWithHeaders(testUrl, {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'
  }, '只有User-Agent');
  
  // 测试3: 添加Referer
  console.log('\n3. 添加User-Agent + Referer');
  await testWithHeaders(testUrl, {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    'Referer': 'https://scholar.google.com/'
  }, 'User-Agent + Referer');
  
  // 测试4: 添加Accept头
  console.log('\n4. 添加Accept头');
  await testWithHeaders(testUrl, {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    'Referer': 'https://scholar.google.com/',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7'
  }, 'User-Agent + Referer + Accept');
  
  // 测试5: 添加Accept-Language
  console.log('\n5. 添加Accept-Language');
  await testWithHeaders(testUrl, {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    'Referer': 'https://scholar.google.com/',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6,zh-TW;q=0.5'
  }, 'User-Agent + Referer + Accept + Accept-Language');
  
  // 测试6: 尝试添加Sec-Fetch-*头（可能被浏览器忽略）
  console.log('\n6. 尝试添加Sec-Fetch-*头');
  await testWithHeaders(testUrl, {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    'Referer': 'https://scholar.google.com/',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6,zh-TW;q=0.5',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'cross-site',
    'Sec-Fetch-User': '?1'
  }, '完整请求头（不含Cookie）');
  
  // 测试7: 尝试不同的Accept头（专门针对PDF）
  console.log('\n7. 尝试PDF专用Accept头');
  await testWithHeaders(testUrl, {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
    'Referer': 'https://scholar.google.com/',
    'Accept': 'application/pdf,text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
  }, 'PDF优先Accept头');

  // 测试8: 完全模拟成功的curl请求
  console.log('\n8. 完全模拟成功的curl请求');
  await testWithHeaders(testUrl, {
    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'referer': 'https://scholar.google.com/',
    'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'
  }, '成功的curl请求头组合');

  // 测试9: 手动重定向跟踪（模拟curl -I）
  console.log('\n9. 手动重定向跟踪');
  await testManualRedirect(testUrl, {
    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'referer': 'https://scholar.google.com/',
    'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'
  });

  console.log('\n=== 测试完成 ===');
}

async function testWithHeaders(url, headers, description) {
  try {
    console.log(`\n测试: ${description}`);
    console.log('请求头:', headers);
    
    const response = await fetch(url, {
      method: 'HEAD',
      headers: headers,
      redirect: 'follow'
    });
    
    const result = {
      ok: response.ok,
      status: response.status,
      url: response.url,
      redirected: response.redirected,
      contentType: response.headers.get('content-type'),
      contentLength: response.headers.get('content-length')
    };
    
    console.log('结果:', result);
    
    // 分析结果
    if (result.redirected) {
      if (result.url.includes('/download/') || result.url.includes('.pdf')) {
        console.log('✅ 可能成功：重定向到了包含download或.pdf的URL');
      } else if (result.url.includes('academia.edu') && !result.url.includes('/download/')) {
        console.log('❌ 重定向到论文详情页，可能需要Cookie');
      } else {
        console.log('🤔 重定向到其他URL，需要进一步分析');
      }
    } else {
      console.log('ℹ️ 没有发生重定向');
    }
    
    if (result.contentType && result.contentType.includes('application/pdf')) {
      console.log('✅ 内容类型是PDF！');
    } else if (result.contentType && result.contentType.includes('text/html')) {
      console.log('❌ 内容类型是HTML');
    }
    
    return result;
    
  } catch (error) {
    console.log(`❌ 请求失败: ${error.message}`);
    return null;
  }
}

async function testManualRedirect(url, headers) {
  try {
    console.log('手动重定向跟踪测试:');
    console.log('请求头:', headers);

    const response = await fetch(url, {
      method: 'HEAD',
      headers: headers,
      redirect: 'manual'
    });

    console.log('第一次响应:', {
      status: response.status,
      location: response.headers.get('location'),
      contentType: response.headers.get('content-type')
    });

    if (response.status >= 300 && response.status < 400) {
      const location = response.headers.get('location');
      if (location) {
        console.log('🔄 检测到重定向到:', location);

        if (location.includes('cloudfront.net') || location.includes('.pdf')) {
          console.log('🎉 成功！重定向到PDF URL！');
          console.log('PDF下载链接:', location);

          // 测试最终的PDF URL
          try {
            const finalResponse = await fetch(location, {
              method: 'HEAD',
              redirect: 'follow'
            });

            console.log('最终PDF响应:', {
              ok: finalResponse.ok,
              status: finalResponse.status,
              contentType: finalResponse.headers.get('content-type'),
              contentLength: finalResponse.headers.get('content-length')
            });

            if (finalResponse.headers.get('content-type')?.includes('application/pdf')) {
              console.log('✅ 确认：最终内容是PDF文件！');
            }
          } catch (finalError) {
            console.log('测试最终PDF URL失败:', finalError.message);
          }
        } else {
          console.log('❌ 重定向到非PDF URL');
        }
      }
    } else {
      console.log('❌ 没有重定向响应');
    }

  } catch (error) {
    console.log('❌ 手动重定向测试失败:', error.message);
  }
}

// 运行测试
testHeaderCombinations();
